["tests/test_api_client.py::TestAINavigatorClient::test_client_initialization", "tests/test_api_client.py::TestAINavigatorClient::test_client_initialization_no_auth_token", "tests/test_api_client.py::TestAINavigatorClient::test_close_session", "tests/test_api_client.py::TestAINavigatorClient::test_context_manager", "tests/test_api_client.py::TestAINavigatorClient::test_ensure_session", "tests/test_api_client.py::TestAINavigatorClient::test_get_headers", "tests/test_api_client.py::TestAINavigatorClient::test_get_headers_with_additional", "tests/test_api_client.py::TestAINavigatorClient::test_get_submission_status", "tests/test_api_client.py::TestAINavigatorClient::test_handle_response_authentication_error", "tests/test_api_client.py::TestAINavigatorClient::test_handle_response_client_error", "tests/test_api_client.py::TestAINavigatorClient::test_handle_response_rate_limit_error", "tests/test_api_client.py::TestAINavigatorClient::test_handle_response_server_error", "tests/test_api_client.py::TestAINavigatorClient::test_handle_response_success", "tests/test_api_client.py::TestAINavigatorClient::test_health_check_failure", "tests/test_api_client.py::TestAINavigatorClient::test_health_check_success", "tests/test_api_client.py::TestAINavigatorClient::test_make_request_success", "tests/test_api_client.py::TestAINavigatorClient::test_parse_rate_limit_headers", "tests/test_api_client.py::TestAINavigatorClient::test_parse_rate_limit_headers_missing", "tests/test_api_client.py::TestAINavigatorClient::test_rate_limiting", "tests/test_api_client.py::TestAINavigatorClient::test_retry_logic", "tests/test_api_client.py::TestAINavigatorClient::test_submit_resource_failure", "tests/test_api_client.py::TestAINavigatorClient::test_submit_resource_success", "tests/test_api_client.py::TestAINavigatorClient::test_submit_resources_batch", "tests/test_api_client.py::TestAINavigatorClient::test_submit_resources_batch_partial_failure", "tests/test_api_client.py::TestAINavigatorClient::test_update_resource", "tests/test_api_client.py::test_client_integration", "tests/test_classification.py::TestContentAnalysisClassifier::test_content_analysis_failure", "tests/test_classification.py::TestContentAnalysisClassifier::test_content_analysis_with_mock_data", "tests/test_classification.py::TestContentScraper::test_content_analysis_for_classification", "tests/test_classification.py::TestContentScraper::test_content_extraction_mock", "tests/test_classification.py::TestHybridLLMClassifier::test_high_confidence_traditional", "tests/test_classification.py::TestHybridLLMClassifier::test_low_confidence_llm_fallback", "tests/test_classification.py::TestLLMClassifier::test_llm_api_failure", "tests/test_classification.py::TestLLMClassifier::test_llm_classification_success", "tests/test_classification.py::TestLLMClassifier::test_llm_no_api_key", "tests/test_classification.py::TestNameBasedClassifier::test_ai_tool_names", "tests/test_classification.py::TestNameBasedClassifier::test_course_names", "tests/test_classification.py::TestNameBasedClassifier::test_research_names", "tests/test_classification.py::TestURLPatternClassifier::test_course_classification", "tests/test_classification.py::TestURLPatternClassifier::test_research_paper_classification", "tests/test_classification.py::TestURLPatternClassifier::test_software_classification", "tests/test_classification.py::TestURLPatternClassifier::test_tool_classification", "tests/test_classification.py::TestURLPatternClassifier::test_unknown_pattern", "tests/test_classification.py::test_classification_integration", "tests/test_collection_pipeline.py::TestConfidenceScoring::test_confidence_calculation", "tests/test_collection_pipeline.py::TestConfidenceScoring::test_confidence_explanation", "tests/test_collection_pipeline.py::TestConfidenceScoring::test_confidence_levels", "tests/test_collection_pipeline.py::TestConfidenceScoring::test_llm_recommendation", "tests/test_collection_pipeline.py::TestDataCollection::test_advanced_collector_deduplication", "tests/test_collection_pipeline.py::TestDataCollection::test_collect_all_scrapers", "tests/test_collection_pipeline.py::TestDataCollection::test_collect_from_single_scraper", "tests/test_collection_pipeline.py::TestDataCollection::test_collect_minimal_entities", "tests/test_collection_pipeline.py::TestDataCollection::test_collector_initialization", "tests/test_collection_pipeline.py::TestDataCollection::test_collector_metrics", "tests/test_collection_pipeline.py::TestDataCollection::test_mock_scraper", "tests/test_collection_pipeline.py::TestDataCollection::test_scraper_management", "tests/test_collection_pipeline.py::TestEntityClassification::test_classification_consistency", "tests/test_collection_pipeline.py::TestEntityClassification::test_classifier_management", "tests/test_collection_pipeline.py::TestEntityClassification::test_entity_classifier", "tests/test_collection_pipeline.py::TestEntityClassification::test_name_based_classifier", "tests/test_collection_pipeline.py::TestEntityClassification::test_url_pattern_classifier", "tests/test_collection_pipeline.py::TestEntityTypeMapping::test_entity_type_mapping", "tests/test_collection_pipeline.py::TestIntegratedPipeline::test_full_pipeline", "tests/test_collection_pipeline.py::TestIntegratedPipeline::test_pipeline_error_handling", "tests/test_collection_pipeline.py::TestIntegratedPipeline::test_pipeline_with_confidence_scoring", "tests/test_collection_pipeline.py::test_performance_benchmarks", "tests/test_integration.py::TestAPIIntegration::test_batch_processor_integration", "tests/test_integration.py::TestAPIIntegration::test_batch_processor_with_failures", "tests/test_integration.py::TestAPIIntegration::test_batch_submission_workflow", "tests/test_integration.py::TestAPIIntegration::test_error_handling_integration", "tests/test_integration.py::TestAPIIntegration::test_full_workflow_integration", "tests/test_integration.py::TestAPIIntegration::test_monitoring_integration", "tests/test_integration.py::TestAPIIntegration::test_rate_limiting_integration", "tests/test_integration.py::TestAPIIntegration::test_retry_logic_integration", "tests/test_integration.py::TestAPIIntegration::test_single_resource_submission_workflow", "tests/test_integration.py::TestAPIIntegration::test_validation_integration", "tests/test_integration.py::test_concurrent_submissions", "tests/test_integration.py::test_configuration_integration", "tests/test_integration.py::test_logging_integration", "tests/test_validation.py::TestResponseValidator::test_extract_errors_multiple_errors", "tests/test_validation.py::TestResponseValidator::test_extract_errors_no_errors", "tests/test_validation.py::TestResponseValidator::test_extract_errors_single_error_dict", "tests/test_validation.py::TestResponseValidator::test_extract_errors_single_error_string", "tests/test_validation.py::TestResponseValidator::test_extract_errors_validation_detail", "tests/test_validation.py::TestResponseValidator::test_extract_errors_validation_detail_string", "tests/test_validation.py::TestResponseValidator::test_sanitize_response_data", "tests/test_validation.py::TestResponseValidator::test_sanitize_response_data_invalid_uuid", "tests/test_validation.py::TestResponseValidator::test_validate_entity_id_invalid_string", "tests/test_validation.py::TestResponseValidator::test_validate_entity_id_invalid_type", "tests/test_validation.py::TestResponseValidator::test_validate_entity_id_none", "tests/test_validation.py::TestResponseValidator::test_validate_entity_id_valid_string", "tests/test_validation.py::TestResponseValidator::test_validate_entity_id_valid_uuid", "tests/test_validation.py::TestResponseValidator::test_validate_resource_data_invalid_url", "tests/test_validation.py::TestResponseValidator::test_validate_resource_data_short_description", "tests/test_validation.py::TestResponseValidator::test_validate_resource_data_short_name", "tests/test_validation.py::TestResponseValidator::test_validate_resource_data_valid", "tests/test_validation.py::TestResponseValidator::test_validate_response_structure_error_without_message", "tests/test_validation.py::TestResponseValidator::test_validate_response_structure_invalid_type", "tests/test_validation.py::TestResponseValidator::test_validate_response_structure_valid", "tests/test_validation.py::TestResponseValidator::test_validate_status_invalid_type", "tests/test_validation.py::TestResponseValidator::test_validate_status_unknown_status", "tests/test_validation.py::TestResponseValidator::test_validate_status_valid", "tests/test_validation.py::TestResponseValidator::test_validate_submission_response_minimal", "tests/test_validation.py::TestResponseValidator::test_validate_submission_response_success", "tests/test_validation.py::TestResponseValidator::test_validate_submission_response_with_errors", "tests/test_validation.py::TestResponseValidator::test_validate_submission_response_with_name_mismatch", "tests/test_validation.py::TestResponseValidator::test_validate_url_invalid_format", "tests/test_validation.py::TestResponseValidator::test_validate_url_invalid_type", "tests/test_validation.py::TestResponseValidator::test_validate_url_none", "tests/test_validation.py::TestResponseValidator::test_validate_url_valid", "tests/test_validation.py::TestResponseValidator::test_validate_url_with_field_name", "tests/test_validation.py::test_response_validation_integration"]