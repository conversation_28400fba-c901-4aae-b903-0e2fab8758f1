"""
Main pipeline orchestrator for AI Resource Enhancement Pipeline.
Manages the end-to-end process: collect → classify → research → enhance → submit.
"""

import asyncio
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import uuid4

from arep.collectors.collector import MinimalDataCollector
from arep.classification.classifier import EntityTypeClassifier
from arep.enhancement.research import SmartResearchEngine
from arep.enhancement.registry import enhancer_registry
from arep.api.client import AINavigatorClient
from arep.models import MinimalEntity, ClassifiedEntity, ProcessingStatus
from arep.monitoring.metrics import MetricsCollector
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class EnhancementPipeline:
    """
    Main pipeline orchestrator that manages the complete entity enhancement process.
    
    Pipeline stages:
    1. Collection: Discover and collect minimal entity data
    2. Classification: Determine entity type and confidence
    3. Research: Gather comprehensive data about the entity
    4. Enhancement: Transform data into API-ready format
    5. Submission: Submit to AI Navigator API
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the enhancement pipeline.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Initialize components
        self.collector = MinimalDataCollector()
        self.classifier = EntityTypeClassifier()
        self.research_engine = SmartResearchEngine()
        self.api_client = AINavigatorClient()
        self.metrics = MetricsCollector()
        
        # Processing state
        self.processing_status: Dict[str, ProcessingStatus] = {}
        self.processed_entities: List[str] = []
        self.failed_entities: List[str] = []
        
        # Configuration
        self.max_concurrent_entities = self.config.get('max_concurrent_entities', 5)
        self.skip_failed_classification = self.config.get('skip_failed_classification', True)
        self.skip_unsupported_types = self.config.get('skip_unsupported_types', True)
        
        logger.info("Enhancement pipeline initialized")
    
    async def run(self) -> Dict[str, Any]:
        """
        Run the complete enhancement pipeline.
        
        Returns:
            Dictionary with processing results and metrics
        """
        logger.info("Starting enhancement pipeline")
        start_time = time.time()
        
        try:
            # Stage 1: Collection
            logger.info("Stage 1: Collecting entities")
            minimal_entities = await self.collector.collect()
            logger.info(f"Collected {len(minimal_entities)} entities")
            
            if not minimal_entities:
                logger.warning("No entities collected, pipeline complete")
                return self._create_results_summary(start_time, [])
            
            # Stage 2-5: Process entities concurrently
            logger.info("Stage 2-5: Processing entities through pipeline")
            results = await self._process_entities_concurrent(minimal_entities)
            
            # Generate final results
            total_time = time.time() - start_time
            logger.info(f"Pipeline completed in {total_time:.2f}s")
            
            return self._create_results_summary(start_time, results)
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise
    
    async def process_single_entity(self, minimal_entity: MinimalEntity) -> Optional[Dict[str, Any]]:
        """
        Process a single entity through the complete pipeline.
        
        Args:
            minimal_entity: Minimal entity data to process
            
        Returns:
            Processing result dictionary or None if failed
        """
        entity_id = str(uuid4())
        entity_name = minimal_entity.name
        
        # Initialize processing status
        status = ProcessingStatus(
            entity_id=entity_id,
            current_stage="collected",
            status="processing",
            started_at=datetime.now()
        )
        self.processing_status[entity_id] = status
        
        logger.info(f"Processing entity: {entity_name}")
        
        try:
            # Stage 2: Classification
            status.current_stage = "classifying"
            classified_entity = await self._classify_entity(minimal_entity)
            
            if not classified_entity:
                if self.skip_failed_classification:
                    logger.warning(f"Skipping entity with failed classification: {entity_name}")
                    status.status = "failed"
                    status.error_message = "Classification failed"
                    return None
                else:
                    raise Exception("Classification failed")
            
            # Check if we have an enhancer for this type
            if not enhancer_registry.has_enhancer(classified_entity.entity_type):
                if self.skip_unsupported_types:
                    logger.warning(f"Skipping unsupported entity type: {classified_entity.entity_type}")
                    status.status = "failed"
                    status.error_message = f"Unsupported entity type: {classified_entity.entity_type}"
                    return None
                else:
                    raise Exception(f"No enhancer for entity type: {classified_entity.entity_type}")
            
            # Stage 3: Research
            status.current_stage = "researching"
            research_data = await self._research_entity(classified_entity)
            
            # Stage 4: Enhancement
            status.current_stage = "enhancing"
            resource = await self._enhance_entity(classified_entity, research_data)
            
            # Stage 5: Submission
            status.current_stage = "submitting"
            submission_result = await self._submit_entity(resource)
            
            # Update status
            status.current_stage = "completed"
            status.status = "completed"
            status.completed_at = datetime.now()
            status.processing_time = (status.completed_at - status.started_at).total_seconds()
            
            self.processed_entities.append(entity_id)
            
            result = {
                'entity_id': entity_id,
                'entity_name': entity_name,
                'entity_type': classified_entity.entity_type,
                'classification_confidence': classified_entity.classification_confidence,
                'submission_result': submission_result,
                'processing_time': status.processing_time
            }
            
            logger.info(f"Successfully processed entity: {entity_name}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process entity {entity_name}: {e}")
            status.status = "failed"
            status.error_message = str(e)
            status.completed_at = datetime.now()
            self.failed_entities.append(entity_id)
            return None
    
    async def _process_entities_concurrent(self, minimal_entities: List[MinimalEntity]) -> List[Dict[str, Any]]:
        """Process multiple entities concurrently."""
        semaphore = asyncio.Semaphore(self.max_concurrent_entities)
        
        async def process_with_semaphore(entity):
            async with semaphore:
                return await self.process_single_entity(entity)
        
        # Create tasks for all entities
        tasks = [process_with_semaphore(entity) for entity in minimal_entities]
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out None results and exceptions
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Task failed with exception: {result}")
            elif result is not None:
                valid_results.append(result)
        
        return valid_results
    
    async def _classify_entity(self, minimal_entity: MinimalEntity) -> Optional[ClassifiedEntity]:
        """Classify a minimal entity."""
        try:
            classification_result = await self.classifier.classify(minimal_entity)
            
            # Create classified entity
            classified_entity = ClassifiedEntity(
                **minimal_entity.model_dump(),
                entity_type=classification_result.entity_type,
                entity_type_id=classification_result.entity_type_id,
                classification_confidence=classification_result.confidence,
                classification_reasoning=classification_result.reasoning,
                alternative_types=classification_result.alternative_types
            )
            
            return classified_entity
            
        except Exception as e:
            logger.error(f"Classification failed for {minimal_entity.name}: {e}")
            return None
    
    async def _research_entity(self, classified_entity: ClassifiedEntity):
        """Research a classified entity."""
        async with self.research_engine as research_engine:
            return await research_engine.research(classified_entity)
    
    async def _enhance_entity(self, classified_entity: ClassifiedEntity, research_data):
        """Enhance entity with type-specific enhancer."""
        enhancer = enhancer_registry.get_enhancer(classified_entity.entity_type)
        if not enhancer:
            raise Exception(f"No enhancer found for type: {classified_entity.entity_type}")
        
        return await enhancer.enhance(classified_entity, research_data)
    
    async def _submit_entity(self, resource):
        """Submit enhanced entity to API."""
        async with self.api_client as client:
            return await client.submit_resource(resource)
    
    def _create_results_summary(self, start_time: float, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create summary of pipeline results."""
        total_time = time.time() - start_time
        
        return {
            'pipeline_completed': True,
            'total_processing_time': total_time,
            'entities_processed': len(results),
            'entities_successful': len(self.processed_entities),
            'entities_failed': len(self.failed_entities),
            'success_rate': len(self.processed_entities) / max(1, len(self.processed_entities) + len(self.failed_entities)),
            'results': results,
            'processing_status': {k: v.model_dump() for k, v in self.processing_status.items()}
        }
