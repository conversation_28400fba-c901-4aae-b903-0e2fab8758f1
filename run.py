#!/usr/bin/env python3
"""
Pipeline runner for AI Resource Enhancement Pipeline.
Executes the complete pipeline with proper error handling and logging.
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from arep.pipeline import Enhancement<PERSON><PERSON>eline
from arep.utils.logger import get_logger, setup_logging
from arep.config import load_config

logger = get_logger(__name__)


class PipelineRunner:
    """
    Runner for the AI Resource Enhancement Pipeline.
    
    Provides command-line interface and configuration management
    for running the complete pipeline.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the pipeline runner.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.config_path = config_path
        self.config = self._load_configuration()
        self.pipeline = None
        
    def _load_configuration(self) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        if self.config_path and Path(self.config_path).exists():
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from: {self.config_path}")
                return config
            except Exception as e:
                logger.error(f"Failed to load config from {self.config_path}: {e}")
                logger.info("Using default configuration")
        
        # Default configuration
        return {
            'max_concurrent_entities': 5,
            'skip_failed_classification': True,
            'skip_unsupported_types': True,
            'log_level': 'INFO',
            'output_file': None,
            'dry_run': False
        }
    
    async def run(self) -> Dict[str, Any]:
        """
        Run the complete enhancement pipeline.
        
        Returns:
            Dictionary with pipeline results
        """
        logger.info("=" * 60)
        logger.info("AI Resource Enhancement Pipeline")
        logger.info("=" * 60)
        logger.info(f"Started at: {datetime.now().isoformat()}")
        
        try:
            # Setup logging based on config
            setup_logging(level=self.config.get('log_level', 'INFO'))
            
            # Initialize pipeline with configuration
            self.pipeline = EnhancementPipeline(config=self.config)
            
            # Check if this is a dry run
            if self.config.get('dry_run', False):
                logger.info("DRY RUN MODE - No entities will be submitted to API")
                return await self._run_dry_run()
            
            # Run the pipeline
            logger.info("Starting pipeline execution...")
            results = await self.pipeline.run()
            
            # Save results if output file specified
            if self.config.get('output_file'):
                await self._save_results(results)
            
            # Print summary
            self._print_summary(results)
            
            return results
            
        except KeyboardInterrupt:
            logger.info("Pipeline interrupted by user")
            return {'pipeline_completed': False, 'error': 'Interrupted by user'}
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return {'pipeline_completed': False, 'error': str(e)}
        finally:
            logger.info(f"Pipeline finished at: {datetime.now().isoformat()}")
            logger.info("=" * 60)
    
    async def _run_dry_run(self) -> Dict[str, Any]:
        """Run pipeline in dry-run mode (no API submissions)."""
        logger.info("Running in dry-run mode...")
        
        # Collect entities
        minimal_entities = await self.pipeline.collector.collect()
        logger.info(f"Would process {len(minimal_entities)} entities")
        
        # Process a few entities through classification and research only
        sample_size = min(3, len(minimal_entities))
        sample_entities = minimal_entities[:sample_size]
        
        dry_run_results = []
        for entity in sample_entities:
            try:
                # Classify
                classified = await self.pipeline._classify_entity(entity)
                if not classified:
                    continue
                
                # Research
                research_data = await self.pipeline._research_entity(classified)
                
                dry_run_results.append({
                    'entity_name': entity.name,
                    'entity_type': classified.entity_type,
                    'classification_confidence': classified.classification_confidence,
                    'research_features_count': len(research_data.features),
                    'has_description': bool(research_data.description)
                })
                
            except Exception as e:
                logger.error(f"Dry run failed for {entity.name}: {e}")
        
        return {
            'pipeline_completed': True,
            'dry_run': True,
            'total_entities': len(minimal_entities),
            'sample_processed': len(dry_run_results),
            'sample_results': dry_run_results
        }
    
    async def _save_results(self, results: Dict[str, Any]):
        """Save pipeline results to file."""
        output_file = self.config['output_file']
        try:
            # Add timestamp to results
            results['timestamp'] = datetime.now().isoformat()
            results['config'] = self.config
            
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Results saved to: {output_file}")
            
        except Exception as e:
            logger.error(f"Failed to save results to {output_file}: {e}")
    
    def _print_summary(self, results: Dict[str, Any]):
        """Print pipeline execution summary."""
        logger.info("\n" + "=" * 50)
        logger.info("PIPELINE EXECUTION SUMMARY")
        logger.info("=" * 50)
        
        if results.get('dry_run'):
            logger.info(f"Dry Run Mode: Processed {results.get('sample_processed', 0)} sample entities")
            logger.info(f"Total entities available: {results.get('total_entities', 0)}")
        else:
            logger.info(f"Total entities processed: {results.get('entities_processed', 0)}")
            logger.info(f"Successful submissions: {results.get('entities_successful', 0)}")
            logger.info(f"Failed submissions: {results.get('entities_failed', 0)}")
            logger.info(f"Success rate: {results.get('success_rate', 0):.1%}")
            logger.info(f"Total processing time: {results.get('total_processing_time', 0):.2f}s")
        
        logger.info("=" * 50)


async def main():
    """Main entry point for the pipeline runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description='AI Resource Enhancement Pipeline')
    parser.add_argument('--config', '-c', help='Path to configuration file')
    parser.add_argument('--dry-run', action='store_true', help='Run in dry-run mode (no API submissions)')
    parser.add_argument('--output', '-o', help='Output file for results')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Create configuration from command line args
    config = {}
    if args.config:
        config['config_path'] = args.config
    if args.dry_run:
        config['dry_run'] = True
    if args.output:
        config['output_file'] = args.output
    if args.log_level:
        config['log_level'] = args.log_level
    
    # Initialize and run pipeline
    runner = PipelineRunner(config_path=args.config)
    runner.config.update(config)  # Override with command line args
    
    try:
        results = await runner.run()
        
        # Exit with appropriate code
        if results.get('pipeline_completed', False):
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Pipeline runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
